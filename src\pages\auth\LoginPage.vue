<script lang="ts">
import { defineComponent, ref } from 'vue';
import { QSpinnerIos, useQuasar } from 'quasar'
import { useRouter } from 'vue-router';
import img1 from 'src/assets/images/image3.png';

export default defineComponent({
  setup() {

    const $q = useQuasar();
    const router = useRouter();
    const loading = ref(false);
    const disabled = ref(false);
    const showPassword = ref(false);
    const slide = ref('style');
    const is_check = ref(false);
    const form = ref({
      username: '',
      password: '',
    });

    const onReset = () => {
      form.value = {
        username: '',
        password: '',
      };
    };

    const onSubmit = () => {
      loading.value = true;
      disabled.value = true;

      $q.loading.show({
        spinner: QSpinnerIos,
        spinnerColor: 'white',
        spinnerSize: 140,
        backgroundColor: 'blue',
        message: '',
        messageColor: 'black',
      });

      setTimeout(() => {
        loading.value = false;
        disabled.value = false;
        $q.loading.hide();
        router.push('/');
      }, 2000);
    };

    return {
      form,
      loading,
      disabled,
      showPassword,
      slide,
      is_check,
      img1,
      onSubmit,
      onReset,
    }
  },
})
</script>

<template>
  <q-layout>
    <q-page-container class="login-page">
      <q-page class="row items-center justify-center" style="height: 100vh;">
        <div class="col-md-7 col-sm-12 hidden-xs hidden-sm carousel-section">
          <q-img :src="img1" style="height: 100%; width: 100%" fit="cover">
            <div class="absolute-bottom text-center text-white bg-transparent">
              <h1 class="text-h4 text-weight-bold">Bienvenue sur Massanou Market Place</h1>
              <p class="text-subtitle1 q-mt-md"
                style="background-color: rgba(0, 0, 0, 0.6); padding: 8px 16px; border-radius: 4px;">
                Gérez vos vendeurs, suivez vos produits et optimisez vos ventes avec notre plateforme
                administrative intuitive.
              </p>
            </div>
          </q-img>
        </div>
        <!-- Section Formulaire -->
        <div class="col-md-5 col-sm-12 form-section">
          <q-card flat bordered class="form-card q-pa-lg">
            <q-card-section>
              <div class="text-h5 text-center text-bold q-pb-md">ADMIN MARKET</div>
              <div class="text-subtitle2 text-center q-pb-md">
                Connectez-vous à votre espace administrateur
              </div>
            </q-card-section>

            <q-card-section>
              <q-form @submit="onSubmit" class="q-gutter-md">
                <!-- Champ Nom d'utilisateur -->
                <q-input outlined dense v-model="form.username" label="Adresse email" lazy-rules
                  :rules="[val => !!val || 'Veuillez entrer votre adresse email']"
                  placeholder="Entrer votre adresse email" autocomplete="off">
                  <template v-slot:prepend>
                    <q-icon name="email" />
                  </template>
                </q-input>

                <!-- Champ Mot de passe -->
                <q-input :type="showPassword ? 'text' : 'password'" outlined dense v-model="form.password"
                  label="Mot de passe" lazy-rules :rules="[val => !!val || 'Veuillez entrer votre mot de passe']"
                  placeholder="Entrer votre mot de passe" autocomplete="off">
                  <template v-slot:prepend>
                    <q-icon name="lock" />
                  </template>
                  <template v-slot:append>
                    <q-btn flat round dense icon="visibility" @click="showPassword = !showPassword" />
                  </template>
                </q-input>

                <!-- Checkbox Se souvenir de moi -->
                <q-checkbox v-model="is_check" label="Se souvenir de moi" />

                <!-- Bouton de connexion -->
                <div class="q-pt-md">
                  <q-btn type="submit" color="primary" class="full-width" label="Se connecter" :loading="loading"
                    :disable="disabled">
                    <q-spinner-dots slot="loading" />
                  </q-btn>
                </div>

                <!-- Lien Mot de passe oublié -->
                <div class="text-center q-mt-md">
                  <q-btn flat color="primary" label="Mot de passe oublié ?" />
                </div>
              </q-form>
            </q-card-section>
          </q-card>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<style scoped>
.login-page {
  background-color: #f5f5f5;
}

.carousel-section {
  padding: 0;
  margin: 0;
  height: 100vh;
}

.form-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.form-card {
  max-width: 400px;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1021px) {
  .carousel-section {
    display: none;
  }

  .form-section {
    width: 100%;
  }
}
</style>
