import { DateTime } from 'luxon';
export type MenuItem = {
  title: string;
  value: string;
  path: string;
  icon?: string;
}

export type Menu = {
  icon?: string;
  text: string;
  value: string;
  path: string;
  childreen: MenuItem[];
}

export interface Response {
  success: boolean;
  message: string;
  result: any;
  errors?: any;
  except?: any;
}

export interface UserToken {
  token: string;
  expires_at: string;
  type?: string;
}
