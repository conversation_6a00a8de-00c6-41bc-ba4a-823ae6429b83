import { Menu } from "src/models";

export const mainLinks: Menu[] = [
  {
    icon: 'dashboard',
    text: 'Dashboard',
    value: 'dashboard',
    path: '/',
    childreen: []
  },
  {
    icon: 'analytics',
    text: "Analytics",
    value: 'analytical',
    path: '/analytical',
    childreen: []
  },
];

export const catalogLinks: Menu[] = [
  {
    text: "Liste des boutiques",
    value: "shops",
    path: "/shops",
    icon: 'store',
    childreen: []
  },
  {
    icon: 'inventory_2',
    text: 'Liste des produits',
    value: 'products',
    path: '/products',
    childreen: []
  },
  {
    icon: 'category',
    text: 'Categories',
    value: 'categories',
    path: '/categories',
    childreen: []
  },
  {
    icon: 'style',
    text: 'Types de produits',
    value: 'product-types',
    path: '/product-types',
    childreen: []
  },
  {
    icon: 'local_offer',
    text: 'Tags',
    value: 'tags',
    path: '/tags',
    childreen: []
  }
];

export const ordersLinks: Menu[] = [
  {
    icon: 'shopping_cart',
    text: 'Liste des commandes',
    value: 'orders',
    path: '/orders',
    childreen: []
  },
  {
    icon: 'swap_horiz',
    text: 'Transactions',
    value: 'transactions',
    path: '/transactions',
    childreen: []
  },

];

export const ecommerceLinks: Menu[] = [
  {
    icon: 'account_balance_wallet',
    text: 'Portefeuilles',
    value: 'wallets',
    path: '/wallets',
    childreen: []
  },
  {
    icon: 'money',
    text: 'Demandes de retrait',
    value: 'withdrawal-requests',
    path: '/withdrawal-requests',
    childreen: []
  },
];

export const usersLinks: Menu[] = [
  {
    icon: 'people',
    text: 'Liste des utilisateurs',
    value: 'users',
    path: '/users',
    childreen: []
  },
  {
    icon: 'history',
    text: 'Journal de connexions',
    value: 'connection-logs',
    path: '/connection-logs',
    childreen: []
  },
];

export const actorsLinks: Menu[] = [
  {
    icon: 'admin_panel_settings',
    text: 'Admins',
    value: 'admins',
    path: '/actors/admins',
    childreen: []
  },
  {
    icon: 'store',
    text: 'Vendeurs',
    value: 'sellers',
    path: '/actors/sellers',
    childreen: []
  },
  {
    icon: 'person',
    text: 'Clients',
    value: 'customers',
    path: '/actors/customers',
    childreen: []
  },
];

export const feedbackLinks: Menu[] = [
  {
    icon: 'star',
    text: 'Reviews',
    value: 'reviews',
    path: '/feedbacks/reviews',
    childreen: []
  },
  {
    icon: 'question_answer',
    text: 'Questions',
    value: 'questions',
    path: '/feedbacks/questions',
    childreen: []
  },
];

export const settingsLinks: Menu[] = [
  {
    icon: 'settings',
    text: 'Paramètres',
    value: 'settings',
    path: '/settings',
    childreen: []
  },
];

const menus = {
  main: mainLinks,
  shops: catalogLinks,
  orders: ordersLinks,
  ecommerce: ecommerceLinks,
  users: usersLinks,
  actors: actorsLinks,
  feedbacks: feedbackLinks,
  settings: settingsLinks,
};

// Réorganisation avec titres adaptés
export const menuSections = [
  {
    title: "Main",
    links: menus.main,
  },
  {
    title: "Shops Management",
    links: menus.shops,
  },
  {
    title: "Etat des commandes",
    links: menus.orders,
  },
  {
    title: "Etat financiers",
    links: menus.ecommerce,
  },
  {
    title: "Users Management",
    links: menus.users,
  },
  {
    title: "Gestion des acteurs",
    links: menus.actors,
  },
  {
    title: "Gestion des feedbacks",
    links: menus.feedbacks,
  },
  {
    title: "Paramètres",
    links: menus.settings,
  },
];
