<script  lang="ts">
import { defineComponent,ref } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { menuSections } from './menus';
import MenuSection from './MenuSection.vue';
import logo from 'src/assets/images/logo.jpg';


export default defineComponent({
  name: 'MainLayout',
  components: {
    MenuSection,
  },
  setup() {
    const $q = useQuasar();
    const router = useRouter();
    const leftDrawerOpen = ref(false);
    const scrollStyles = {
      thumbStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
      barStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
    };

    const user = {
      username: '<PERSON>',
    };

    const onLogout = () => {
      router.push('/auth/login');
    };

    const toggleLeftDrawer = () => {
      leftDrawerOpen.value = !leftDrawerOpen.value;
    };

    return {
      leftDrawerOpen,
      scrollStyles,
      user,
      logo,
      onLogout,
      toggleLeftDrawer,
      menuSections,
    };
  },
});


</script>

<template>
  <q-layout view="lHh Lpr lFf" style="height: 400px" class="shadow-2 rounded-borders">
    <q-header elevated class="bg-primary text-white q-py-xs shadow-sm" height-hint="60px" >
      <q-toolbar>
        <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer" />

        <q-toolbar-title>
          Espace Administrateur
        </q-toolbar-title>

        <div class="q-gutter-sm row items-center no-wrap">
          <q-btn color="white" @click="$q.fullscreen.toggle()"
            :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'" flat size="md" />

          <q-btn round dense flat color="white" icon="notifications">
            <q-badge color="red" text-color="white" floating>
              0
            </q-badge>
            <q-tooltip>Notifications</q-tooltip>
          </q-btn>
          <q-btn-dropdown flat color="white" icon="person" :label="user.username" no-icon-animation no-caps>
            <q-list>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Profile</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Paramètre</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable v-ripple @click="onLogout">
                <q-item-section>
                  <q-item-label>Déconnexion</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered class=" sidebar text-dark " :width="265"
      :breakpoint="600"  >
      <div class="absolute-top img-header ">
        <div style="display: flex; align-items: center;">
          <q-avatar size="50px" style="margin-right: 8px;">
            <q-img :src="logo" cover />
          </q-avatar>
          <div>
            <span class="text-h6 text-primary">MASSANOU MARKET</span>
          </div>
        </div>
      </div>
      <q-scroll-area class="fit scrollarea" :thumb-style="scrollStyles.thumbStyle" :bar-style="scrollStyles.barStyle">
        <q-list padding class="q-px-sm" style="padding-bottom: 80px !important;">
          <template v-for="(section, index) in menuSections" :key="index">
            <MenuSection :title="section.title" :links="section.links" />
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container class="main">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<style>
.app-header {
  background-color: white;
}

.main {
  background-color: #F3F4F6;
  overflow: hidden;
}

.scrollarea {
  height: calc(100vh - 170px);
  margin-top: 70px;
  border-right: none;
}

.sidebar {
  background: #fcfcfa;
  /* background: linear-gradient(90deg, rgba(255, 255, 255, 1) 10%, rgba(102, 204, 153, 1) 90%); */
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
  border: none;
  position: relative;
}

.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
}

.absolute-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.img-header {
  width: 96%;
  height: 70px;
  display: flex;
  margin-left: 10px;
  align-items: start;
  justify-content: center;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 0.5px;
  height: 0.5px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  opacity: 0.7;
  height: 0.5px;
}

</style>
