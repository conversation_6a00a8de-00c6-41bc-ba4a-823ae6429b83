import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'login',
    path: '/auth/login',
    component: () => import('pages/auth/LoginPage.vue'),
  },
  {
    name: 'main',
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        name: 'dashboard',
        path: '',
        component: () => import('pages/core/dashboard/DashboardPage.vue'),
      },
      {
        name: 'analytical',
        path: 'analytical',
        component: () => import('pages/core/dashboard/AnalyticalPage.vue'),
      },
      {
        name: 'shops',
        path: 'shops',
        component: () => import('pages/core/shops-managment/shops/ShopPage.vue'),
      },
      {
        name: 'products',
        path: 'products',
        component: () => import('pages/core/shops-managment/products/ProductPage.vue'),
      },
      {
        name: 'categories',
        path: 'categories',
        component: () => import('pages/core/shops-managment/categories/CategoryPage.vue'),
      },
      {
        name: 'product-types',
        path: 'product-types',
        component: () => import('pages/core/shops-managment/product-types/ProductTypePage.vue'),
      },
      {
        name: 'tags',
        path: 'tags',
        component: () => import('pages/core/shops-managment/tags/TagsPage.vue'),
      },
      {
        name: 'orders',
        path: 'orders',
        component: () => import('pages/core/orders/OrdersPage.vue'),
      },
      {
        name: 'transactions',
        path: 'transactions',
        component: () => import('pages/core/financials/transactions/TransactionPage.vue'),
      },
      {
        name: 'detail-order',
        path: 'orders/:id/details',
        component: () => import('pages/core/orders/DetailOrderPage.vue'),
      },
      {
        name: 'detail-transaction',
        path: 'transactions/:id/details',
        component: () => import('pages/core/financials/transactions/DetailTransactionPage.vue'),
      },
      {
        name: 'wallets',
        path: 'wallets',
        component: () => import('pages/core/financials/wallets/WalletPage.vue'),
      },
      {
        name: 'withdrawal-requests',
        path: 'withdrawal-requests',
        component: () => import('pages/core/financials/withdrawals/WithdrawalPage.vue'),
      },
      {
        name: 'users',
        path: 'users',
        component: () => import('pages/core/users/UsersPage.vue'),
      },
      {
        name: 'connection-logs',
        path: 'connection-logs',
        component: () => import('pages/core/users/JournalPage.vue'),
      },
      {
        name: 'admins',
        path: 'actors/admins',
        component: () => import('pages/core/actors/admins/AdminPage.vue'),
      },
      {
        name: 'sellers',
        path: 'actors/sellers',
        component: () => import('pages/core/actors/vendors/VendorsPage.vue'),
      },
      {
        name: 'detail-seller',
        path: 'actors/sellers/:id/details',
        component: () => import('pages/core/actors/vendors/DetailSellerPage.vue'),
      },
      {
        name: 'customers',
        path: 'actors/customers',
        component: () => import('pages/core/actors/customers/DetailCustomerPage.vue'),
      },
      {
        name: 'reviews',
        path: 'feedbacks/reviews',
        component: () => import('pages/core/feedbacks/ReviewsPage.vue'),
      },
      {
        name: 'questions',
        path: 'feedbacks/questions',
        component: () => import('pages/core/feedbacks/QuestionsPage.vue'),
      },
      {
        name: 'settings',
        path: 'settings',
        component: () => import('pages/core/settings/SettingPage.vue'),
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
