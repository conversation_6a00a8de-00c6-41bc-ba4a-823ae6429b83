<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'DashboardPage',
  setup() {
    // Données de démonstration pour les statistiques
    const summaryStats = ref([
      {
        title: 'Total Revenue',
        value: '$486.15',
        icon: 'trending_up',
        color: 'green',
        bgColor: 'green-1'
      },
      {
        title: 'Total Order',
        value: '16',
        icon: 'shopping_cart',
        color: 'purple',
        bgColor: 'purple-1'
      },
      {
        title: 'Vendor',
        value: '2',
        icon: 'store',
        color: 'pink',
        bgColor: 'pink-1'
      },
      {
        title: 'Total Shops',
        value: '10',
        icon: 'storefront',
        color: 'red',
        bgColor: 'red-1'
      }
    ]);

    const orderStats = ref([
      {
        title: 'Pending Order',
        value: '0',
        icon: 'pending',
        color: 'blue',
        bgColor: 'blue-1'
      },
      {
        title: 'Processing Order',
        value: '0',
        icon: 'settings',
        color: 'blue',
        bgColor: 'blue-1'
      },
      {
        title: 'Completed Order',
        value: '0',
        icon: 'check_circle',
        color: 'orange',
        bgColor: 'orange-1'
      },
      {
        title: 'Cancelled Order',
        value: '0',
        icon: 'cancel',
        color: 'yellow-8',
        bgColor: 'yellow-1'
      }
    ]);

    const timeFilters = ref(['Today', 'Weekly', 'Monthly', 'Yearly']);
    const selectedTimeFilter = ref('Today');

    return {
      summaryStats,
      orderStats,
      timeFilters,
      selectedTimeFilter
    };
  }
});
</script>

<template>
  <q-page class="q-pa-sm">
    <!-- Section Summary -->
    <div class="q-mb-md">
      <div class="row items-center q-mb-md">
        <div class="col">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Summary</h5>
        </div>
      </div>

      <div class="row q-gutter-sm">
        <div v-for="(stat, index) in summaryStats" :key="index" class="col-md-3 col-sm-6  col-xs-12">
          <q-card class="my-card q-pa-md" flat bordered>
            <div class="row items-center">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Section Order Status -->
    <div>
      <div class="row items-center justify-between q-mb-md">
        <div class="col-auto">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Order Status</h5>
        </div>
        <div class="col-auto">
          <div class="row q-gutter-xs">
            <q-btn v-for="filter in timeFilters" :key="filter" :label="filter"
              :color="selectedTimeFilter === filter ? 'primary' : 'grey-5'"
              :text-color="selectedTimeFilter === filter ? 'white' : 'grey-7'" size="sm" dense no-caps
              @click="selectedTimeFilter = filter" :class="selectedTimeFilter === filter ? '' : 'bg-grey-1'" />
          </div>
        </div>
      </div>

      <div class="row q-gutter-sm">
        <div v-for="(stat, index) in orderStats" :key="index" class="col-md-3 col-sm-6  col-xs-12">
          <q-card class="my-card q-pa-md" flat bordered>
            <div class="row items-center">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<style scoped>
.my-card {
  border-radius: 12px;
  /* position: relative; */
  transition: all 0.3s ease;
}

.my-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
