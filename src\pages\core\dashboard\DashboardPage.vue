<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'DashboardPage',
  setup() {
    // Données de démonstration pour les statistiques
    const summaryStats = ref([
      {
        title: 'Revenu Total',
        value: '$486.15',
        icon: 'trending_up',
        color: 'green',
        bgColor: 'green-1'
      },
      {
        title: 'Total Commandes',
        value: '16',
        icon: 'shopping_cart',
        color: 'purple',
        bgColor: 'purple-1'
      },
      {
        title: 'Vendeurs',
        value: '2',
        icon: 'store',
        color: 'pink',
        bgColor: 'pink-1'
      },
      {
        title: 'Total Boutiques',
        value: '10',
        icon: 'storefront',
        color: 'red',
        bgColor: 'red-1'
      }
    ]);

    const orderStats = ref([
      {
        title: 'Commandes en Attente',
        value: '0',
        icon: 'pending',
        color: 'blue',
        bgColor: 'blue-1'
      },
      {
        title: 'Commandes en Cours',
        value: '0',
        icon: 'settings',
        color: 'blue',
        bgColor: 'blue-1'
      },
      {
        title: 'Commandes Terminées',
        value: '0',
        icon: 'check_circle',
        color: 'orange',
        bgColor: 'orange-1'
      },
      {
        title: 'Commandes Annulées',
        value: '0',
        icon: 'cancel',
        color: 'yellow-8',
        bgColor: 'yellow-1'
      }
    ]);

    const timeFilters = ref([
      { label: "Aujourd'hui", value: 'today' },
      { label: 'Hebdomadaire', value: 'weekly' },
      { label: 'Mensuel', value: 'monthly' },
      { label: 'Annuel', value: 'yearly' }
    ]);
    const selectedTimeFilter = ref('today');

    return {
      summaryStats,
      orderStats,
      timeFilters,
      selectedTimeFilter
    };
  }
});
</script>

<template>
  <q-page class="q-pa-md">
    <!-- Section Summary -->
    <q-card class="q-mb-lg q-pa-md " flat>
      <div class="row items-center q-mb-md">
        <div class="col">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Récapitulatif</h5>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(stat, index) in summaryStats" :key="index" class="col-md-3 col-sm-6 col-xs-12">
          <q-card class="my-card q-pa-md" flat bordered>
            <div class="row items-center q-mb-md ">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <!-- Section Order Status -->
    <q-card class="q-mb-lg q-pa-md" flat>
      <div class="row items-center justify-between q-mb-md">
        <div class="col-auto">
          <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Statistiques des Commandes</h5>
        </div>
        <div class="col-auto">
          <div class="row q-col-gutter-xs">
            <q-btn-toggle v-model="selectedTimeFilter" :options="timeFilters" spread rounded unelevated
              class="custom-toggle" toggle-color="primary" color="grey-3" text-color="grey-8" size="sm" />
          </div>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div v-for="(stat, index) in orderStats" :key="index" class="col-md-3 col-sm-6 col-xs-12">
          <q-card class="my-card q-pa-md" flat bordered>
            <div class="row items-center q-mb-md">
              <div class="col">
                <div class="text-caption text-grey-6 q-mb-xs">{{ stat.title }}</div>
                <div class="text-h5 text-weight-bold text-grey-8">{{ stat.value }}</div>
              </div>
              <div class="col-auto">
                <q-avatar :color="stat.bgColor" :text-color="stat.color" size="50px">
                  <q-icon :name="stat.icon" size="24px" />
                </q-avatar>
              </div>
            </div>
            <!-- Barre de couleur en bas -->
            <div class="absolute-bottom" :class="`bg-${stat.color}`" style="height: 4px; width: 100%;"></div>
          </q-card>
        </div>
      </div>
    </q-card>

    <div class="row q-col-gutter-sm q-mb-lg">
      <div class="col-12 col-md-12 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="height: 450px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Commandes récentes</h5>
            </div>
          </div>
          <!-- Contenu de la table des commandes récentes -->
          <div class="text-center text-grey-5 q-mt-xl">
            <q-icon name="receipt_long" size="48px" class="q-mb-md" />
            <div>Aucune commande récente</div>
          </div>
        </q-card>
      </div>
    </div>

    <!-- Section Graphiques et Produits Populaires -->
    <div class="row q-col-gutter-sm">
      <!-- Historique des ventes -->
      <div class="col-12 col-md-8 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="height: 400px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Historique des ventes</h5>
            </div>
            <div class="col-auto">
              <q-btn-dropdown color="primary" label="Cette semaine" size="sm" outline no-caps class="">
                <q-list>
                  <q-item clickable v-close-popup>
                    <q-item-section>
                      <q-item-label>Aujourd'hui</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable v-close-popup>
                    <q-item-section>
                      <q-item-label>Cette semaine</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable v-close-popup>
                    <q-item-section>
                      <q-item-label>Ce mois</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable v-close-popup>
                    <q-item-section>
                      <q-item-label>Cette année</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-btn-dropdown>
            </div>
          </div>
          <!-- Placeholder pour le graphique -->
          <div class="text-center text-grey-5 q-mt-xl">
            <q-icon name="trending_up" size="48px" class="q-mb-md" />
            <div>Graphique des ventes</div>
            <div class="text-caption">Intégration graphique à venir</div>
          </div>
        </q-card>
      </div>

      <!-- Produits populaires -->
      <div class="col-12 col-md-4 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="height: 400px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Produits populaires</h5>
            </div>
          </div>
          <!-- Liste des produits populaires -->
          <q-scroll-area style="height: 300px;">
            <div class="q-gutter-sm">
              <div v-for="i in 10" :key="i" class="row items-center q-py-sm">
                <div class="col-auto">
                  <q-avatar size="40px" color="grey-3">
                    <q-icon name="inventory_2" color="grey-6" />
                  </q-avatar>
                </div>
                <div class="col q-ml-sm">
                  <div class="text-body2 text-weight-medium">Produit {{ i }}</div>
                  <div class="text-caption text-grey-6">{{ 150 - i * 10 }} ventes</div>
                </div>
                <div class="col-auto">
                  <div class="text-body2 text-weight-bold text-green-6">${{ (50 + i * 10) }}.00</div>
                </div>
              </div>
            </div>
          </q-scroll-area>

          <!-- Message si pas de données -->
          <div v-if="false" class="text-center text-grey-5 q-mt-xl">
            <q-icon name="star" size="48px" class="q-mb-md" />
            <div>Aucun produit populaire</div>
          </div>
        </q-card>
      </div>
    </div>

    <div class="row q-col-gutter-sm q-pt-md">
      <div class="col-12 col-md-12 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="height: 400px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Nouveaux clients du mois</h5>
            </div>
          </div>
          <!-- Liste des nouveaux clients -->

          <!-- Message si pas de nouveaux clients -->
          <div v-if="false" class="text-center text-grey-5 q-mt-xl">
            <q-icon name="people" size="48px" class="q-mb-md" />
            <div>Aucun nouveau client ce mois-ci</div>
          </div>
        </q-card>
      </div>
    </div>

    <!-- Section Top Products et Categories -->
    <div class="row q-col-gutter-sm q-mt-lg">
      <!-- Top 10 Most Rated Products -->
      <div class="col-12 col-md-6 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="min-height: 400px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Top 10 Most Rated Products</h5>
            </div>
          </div>

          <!-- Placeholder pour le contenu -->
          <div class="text-center text-grey-5 q-mt-xl">
            <q-icon name="star_rate" size="48px" class="q-mb-md" />
            <div class="text-body1">Produits les mieux notés</div>
            <div class="text-caption">Le tableau sera ajouté dans components/tables</div>
          </div>
        </q-card>
      </div>

      <!-- Top 10 Category with most products -->
      <div class="col-12 col-md-6 col-sm-12 col-xs-12">
        <q-card class="q-pa-md" flat bordered style="min-height: 400px;">
          <div class="row items-center q-mb-md">
            <div class="col">
              <h5 class="text-h6 q-ma-none text-weight-medium text-grey-8">Top 10 Category with most products</h5>
            </div>
          </div>

          <!-- Placeholder pour le contenu -->
          <div class="text-center text-grey-5 q-mt-xl">
            <q-icon name="category" size="48px" class="q-mb-md" />
            <div class="text-body1">Catégories avec le plus de produits</div>
            <div class="text-caption">Le tableau sera ajouté dans components/tables</div>
          </div>
        </q-card>
      </div>
    </div>


  </q-page>
</template>

<style scoped>
.my-card {
  border-radius: 12px;
  position: relative;
  transition: all 0.3s ease;
  height: 120px;
}

.my-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
